# 生产环境配置
app:
  name: "Sec Flow Server"
  version: "1.0.0"
  env: "prod"
  debug: false

server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: 60s
  write_timeout: 60s
  idle_timeout: 60s

database:
  driver: "mysql"
  host: "rm-2ze2ru6h21b05f4v9.mysql.rds.aliyuncs.com"
  port: 3306
  username: "u_insight_rw"
  password: "ENC(f9OCS2YswKRWgXgqaNdJTXL54iVD/Yuv8ln18/N2hpsJV2Ay/5aLBYA5orwtPDRJ)"
  database: "insight"
  charset: "utf8mb4"
  parse_time: true
  loc: "Local"
  max_idle_conns: 50
  max_open_conns: 500
  conn_max_lifetime: 3600s
  log_level: "silent"
  auto_migrate: false  # 生产环境禁用自动迁移，使用DBA管理的SQL脚本



log:
  level: "error"
  format: "json"
  output: "file"
  file_path: "/var/log/sec-flow/app.log"
  max_size: 500
  max_backups: 30
  max_age: 90
  compress: true

scheduler:
  heartbeat_interval: 30s
  heartbeat_timeout: 90s
  active_instance_timeout: 60s
  preprocess_lock_timeout: 90s
  task_assignment_timeout: 180s

cors:
  allow_origins:
    - "https://sec.sy.soyoung.com"
    - "https://sec-release.sy.soyoung.com"
    - "https://security8.soyoung.com"
    - "https://security.soyoung.com"
  allow_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allow_headers:
    - "Content-Type"
    - "Authorization"
    - "X-Requested-With"
  allow_credentials: true

jwt:
  secret: "ENC(aTMJlNi3ZXuy4A9D1cbo9Eh+kTEDuQRP1ZFsq81IO5F2bZEPEUVKRgCRuVJCOyrEhWFEZ6ZxCOTNBeUl)"

other:
  aes_key: "ENC(MceWHeTm33J9BYXfBc7VQm7ZtmxOZSVsEq9vbgfjkMrylSxb7WlFqk51+HGgo3hVBQiBttUkKzWkOZHa)"

third:
  lark:
    app_id: "cli_9f8fe851c0b4d00e"
    app_secret: "ENC(zYOFOvKtSLeIjp/BfrjB14C9jc+6BUDq3/OQHDMgQZA29eRaIwPtL/qSOcfEe51MiodIcxts+sPSXr1y)"
  mongo:
    address: "dds-2zeb56a5379411a42.mongodb.rds.aliyuncs.com:3717,dds-2zeb56a5379411a41.mongodb.rds.aliyuncs.com:3717"
    username: "root"
    password: "ENC(L/edakrcO0jYtK38et9OezjJm7MCM4Ylg+cQ8vs6b7iSGd5huGYyJkvG3UMUYWSZ)"
  aliyun:
    access_key: "ENC(F+K11cvVceUU6ktnhmoq8XP445PWnQEESlJMGjUTvjX5kL2s4BBQTKGzvnVCfX7JaD9jhubBPiPyqA==)"
    access_key_id: "ENC(wbJpcnLV7TlkXllr9v+iGXIhVj2utt0qAVtSTyakNSPxWcVPzplA0eTpgEaRFVdhqtwp0A==)"
  yongan:
    snkey: "ENC(5+vgCLybPpQkpnrAteV0fwpklk1p9d8qSMPcALcWze6eupiqovWhBv/wboU7+a0g+OPEaJvSddU+DpCz)"
    snuser: "LZe5mDdbvxoQfqlk"


databases:
  - host: "polar-public.mysql.polardb.rds.aliyuncs.com"
    username: "u_security_rd"
    password: "ENC(3Hez/oakh5905V99n/k9rh5fzwd/ef+PEnxSkQPrYPfhd50XprdBw7uG6KqucKPQ)"
    database: "db_groupbuy"
  - host: "rm-2zeg68m78e01k7v50.mysql.rds.aliyuncs.com"
    username: "u_security_rw"
    password: "ENC(wUNw5Egc+Trp04ApHt39G4bv4FKqLay3bOZ7K7GYlemn8L8Ul+ygYpi7lb0xM0H1)"
    database: "db_security"