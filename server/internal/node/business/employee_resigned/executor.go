package employee_resigned

import (
	"context"
	"fmt"
	"log"
	"sec-flow-server/internal/node/base"
	"sec-flow-server/internal/service"
	"sec-flow-server/internal/utils"
)

// EmployeeResignedScanNode 员工离职扫描节点
type EmployeeResignedScanNode struct {
	config          *base.NodeConfig
	employeeService *service.EmployeeServiceStruct
}

// NewEmployeeResignedScanNode 创建员工离职扫描节点
func NewEmployeeResignedScanNode() *EmployeeResignedScanNode {
	return &EmployeeResignedScanNode{
		config:          GetNodeConfig(),
		employeeService: &service.EmployeeServiceStruct{},
	}
}

// GetID 获取节点ID
func (n *EmployeeResignedScanNode) GetID() string {
	return n.config.ID
}

// GetConfig 获取节点配置
func (n *EmployeeResignedScanNode) GetConfig() *base.NodeConfig {
	return n.config
}

// Validate 验证节点参数
func (n *EmployeeResignedScanNode) Validate(params map[string]interface{}) error {
	// 验证必填字段
	executeDayField, ok := params["executeDayField"].(string)
	if !ok || executeDayField == "" {
		return fmt.Errorf("executeDayField is required")
	}

	flushStatus, ok := params["flushStatus"].(string)
	if !ok || flushStatus == "" {
		return fmt.Errorf("flushStatus is required")
	}

	outputField, ok := params["outputField"].(string)
	if !ok || outputField == "" {
		return fmt.Errorf("outputField is required")
	}

	// 验证flushStatus值
	if flushStatus != "true" && flushStatus != "false" {
		return fmt.Errorf("flushStatus must be 'true' or 'false'")
	}

	return nil
}

// Execute 执行节点逻辑
func (n *EmployeeResignedScanNode) Execute(ctx context.Context, input *base.ExecutionInput) (*base.ExecutionOutput, error) {
	log.Printf("执行员工离职扫描节点: %s", input.NodeID)

	// 验证参数
	if err := n.Validate(input.Params); err != nil {
		return &base.ExecutionOutput{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	// 获取参数
	executeDayField := input.Params["executeDayField"].(string)
	flushStatusStr := input.Params["flushStatus"].(string)
	outputField := input.Params["outputField"].(string)

	log.Printf("参数 - executeDayField: %s, flushStatus: %s, outputField: %s",
		executeDayField, flushStatusStr, outputField)

	// 从流程数据中获取执行日期
	executeDayStr := ""
	if executeDayValue, exists := utils.GetFieldValue(input.FlowData, executeDayField); exists && executeDayValue != nil {
		executeDayStr = fmt.Sprintf("%v", executeDayValue)
	}

	log.Printf("从字段 %s 获取到执行日期: %s", executeDayField, executeDayStr)

	// 转换flushStatus为布尔值
	flushStatus := flushStatusStr == "true"

	// 调用员工服务扫描离职员工
	resignedEmployees, err := n.employeeService.ScanEmployeeResigned(executeDayStr, flushStatus)
	if err != nil {
		return &base.ExecutionOutput{
			Success: false,
			Error:   fmt.Sprintf("扫描离职员工失败: %v", err),
		}, nil
	}

	log.Printf("扫描到 %d 名离职员工", len(resignedEmployees))

	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 将结果添加到流程数据中 (支持JSON路径表达式)
	if !utils.SetFieldValue(resultFlowData, outputField, resignedEmployees) {
		// 如果设置失败，回退到简单字段设置
		resultFlowData[outputField] = resignedEmployees
	}

	return &base.ExecutionOutput{
		Success:  true,
		FlowData: resultFlowData,
		Message:  fmt.Sprintf("成功扫描到 %d 名离职员工", len(resignedEmployees)),
	}, nil
}

// Example 生成示例数据
func (n *EmployeeResignedScanNode) Example(input *base.ExampleInput) *base.ExampleOutput {
	// 获取配置参数
	outputField := "resignedEmployees"

	if input.Config != nil {
		if of, ok := input.Config["outputField"].(string); ok && of != "" {
			outputField = of
		}
	}

	// 复制输入的流程数据
	resultFlowData := make(map[string]interface{})
	if input.FlowData != nil {
		for k, v := range input.FlowData {
			resultFlowData[k] = v
		}
	}

	// 生成示例离职员工数据
	exampleResignedEmployees := []map[string]interface{}{
		{
			"id":             1,
			"emp_id":         "EMP001",
			"soyoung_uid":    12345,
			"dept_name":      "技术部",
			"full_name":      "张三",
			"name":           "张三",
			"user_base_city": "北京",
			"email":          "<EMAIL>",
			"username":       "zhangsan",
			"lark_user_id":   "ou_123456789",
			"enc_mobile":     "138****1234",
			"emp_type":       1,
			"emp_type_name":  "正式员工",
			"join_time":      "2023-01-15",
			"is_resigned":    1,
			"certified_type": 2,
			"certified_id":   "H001",
			"hospital_name":  "示例医院",
		},
		{
			"id":             2,
			"emp_id":         "EMP002",
			"soyoung_uid":    67890,
			"dept_name":      "产品部",
			"full_name":      "李四",
			"name":           "李四",
			"user_base_city": "上海",
			"email":          "<EMAIL>",
			"username":       "lisi",
			"lark_user_id":   "ou_987654321",
			"enc_mobile":     "139****5678",
			"emp_type":       1,
			"emp_type_name":  "正式员工",
			"join_time":      "2023-03-20",
			"is_resigned":    1,
			"certified_type": 0,
			"certified_id":   0,
			"hospital_name":  "",
		},
	}

	// 将示例数据添加到流程数据中 (支持JSON路径表达式)
	if !utils.SetFieldValue(resultFlowData, outputField, exampleResignedEmployees) {
		// 如果设置失败，回退到简单字段设置
		resultFlowData[outputField] = exampleResignedEmployees
	}

	// 记录数据变化
	changes := []base.DataChange{
		{
			Field:       outputField,
			Action:      "add",
			NewValue:    exampleResignedEmployees,
			Description: "扫描离职员工信息",
		},
	}

	return &base.ExampleOutput{
		FlowData:    resultFlowData,
		Changes:     changes,
		Description: fmt.Sprintf("扫描到 %d 名离职员工，包含员工基本信息、部门信息、认证信息等", len(exampleResignedEmployees)),
	}
}
