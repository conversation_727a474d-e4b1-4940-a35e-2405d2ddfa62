package employee_resigned

import "sec-flow-server/internal/node/base"

// GetNodeConfig 获取员工离职扫描节点配置
func GetNodeConfig() *base.NodeConfig {
	return &base.NodeConfig{
		ID:          "employee_resigned",
		Name:        "员工离职名单",
		Description: "扫描已离职员工信息，支持更新离职状态",
		Category:    "business",
		NodeType:    "rect",
		Icon:        "user-minus",
		FormFields: []base.FormField{
			{
				Key:          "executeDayField",
				Label:        "执行判断日期取值字段",
				Type:         "input",
				Required:     true,
				DefaultValue: "",
				Placeholder:  "支持JSON路径，如: executeInfo.thisDay",
				Validation: map[string]interface{}{
					"minLength": 1,
					"maxLength": 100,
				},
			},
			{
				Key:          "flushStatus",
				Label:        "是否更新人员离职状态",
				Type:         "radio",
				Required:     true,
				DefaultValue: "false",
				Options: []base.FormFieldOption{
					{Label: "是", Value: "true"},
					{Label: "否", Value: "false"},
				},
			},
			{
				Key:          "outputField",
				Label:        "结果输出字段",
				Type:         "input",
				Required:     true,
				DefaultValue: "resignedEmployees",
				Placeholder:  "输出字段名称，如: resignedEmployees",
				Validation: map[string]interface{}{
					"minLength": 1,
					"maxLength": 50,
				},
			},
		},
	}
}
