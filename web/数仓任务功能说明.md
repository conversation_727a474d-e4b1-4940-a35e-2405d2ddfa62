# 数仓任务功能说明

## 功能概述

数仓任务是一个基于LogicFlow的可视化任务编排工具，专门用于数据仓库任务的设计和管理。该功能与流程管理、事件管理、系统管理同级，提供了独立的任务编排界面。

## 主要特性

### 1. 节点类型
- **红色节点**：用于表示数据处理类任务
- **绿色节点**：用于表示数据分析类任务
- 每个节点包含三个配置参数：
  - 任务名称
  - 执行时间
  - 逻辑描述

### 2. 节点配置
每个节点都可以配置以下信息：
- **任务名称**：最多50个字符，用于标识任务，显示在节点标题栏
- **执行时间**：最多50个字符，文本输入框，如"每天09:00"或"2024-01-15 10:30:00"
- **逻辑描述**：最多200个字符，详细描述任务的业务逻辑

### 3. 数据存储
- 使用浏览器本地存储（localStorage）保存任务数据
- 数据格式为JSON，包含节点和连线信息
- 每次保存会记录时间戳

### 4. 界面操作
- **添加红色节点**：点击"添加红色节点"按钮
- **添加绿色节点**：点击"添加绿色节点"按钮
- **编辑节点**：点击节点右上角的编辑按钮（✏️）
- **连接节点**：拖拽节点的锚点创建连线
- **保存数据**：点击"保存"按钮将数据保存到本地
- **清空画布**：点击"清空"按钮清除所有节点和连线

## 使用方法

### 1. 访问功能
在主导航菜单中点击"数仓任务"进入功能页面。

### 2. 创建任务流程
1. 点击"添加红色节点"或"添加绿色节点"
2. 在弹出的配置表单中填写：
   - 任务名称（必填，最多50字符）
   - 执行时间（必填，最多50字符，文本输入）
   - 逻辑描述（必填，最多200字符）
3. 点击"确定"创建节点

### 3. 连接节点
- 将鼠标悬停在节点边缘，会显示锚点
- 拖拽锚点到另一个节点的锚点，创建连线
- 连线表示任务之间的依赖关系

### 4. 编辑节点
- 点击节点右上角的编辑按钮（✏️）
- 修改节点配置信息
- 点击"确定"保存修改

### 5. 保存和加载
- 点击"保存"按钮将当前设计保存到本地
- 下次打开页面时会自动加载之前保存的数据
- 如果没有保存的数据，会显示示例节点

## 节点样式说明

### 红色节点
- 背景：红色渐变
- 边框：红色
- 图标：🔴
- 适用于：数据清洗、数据转换等处理类任务

### 绿色节点
- 背景：绿色渐变
- 边框：绿色
- 图标：🟢
- 适用于：数据分析、报表生成等分析类任务

### 节点信息显示
每个节点都会清晰显示：
- 节点图标和任务名称（在头部标题栏）
- 执行时间（一行显示）
- 逻辑描述（最多两行显示，支持长文本截断）

## 技术实现

### 前端技术栈
- React + TypeScript
- Ant Design UI组件库
- LogicFlow流程图引擎
- dayjs日期处理库

### 核心组件
- `DataWarehouseTask.tsx`：主页面组件
- `CustomDataWarehouseNode.tsx`：自定义节点组件
- `DataWarehouseNodeConfigForm.tsx`：节点配置表单

### 数据存储
- 使用localStorage进行本地存储
- 存储键：`dataWarehouseTaskData`
- 数据格式：JSON，包含nodes、edges和timestamp

## 注意事项

1. **数据持久化**：数据仅保存在浏览器本地，清除浏览器数据会丢失
2. **浏览器兼容性**：需要支持localStorage的现代浏览器
3. **文本长度限制**：任务名称最多50字符，逻辑描述最多200字符
4. **示例数据**：首次使用会自动创建示例节点，可以通过"清空"按钮删除

## 扩展建议

1. **导出功能**：支持导出为JSON文件
2. **导入功能**：支持从JSON文件导入
3. **模板功能**：预设常用的任务模板
4. **执行监控**：集成任务执行状态监控
5. **版本管理**：支持任务流程的版本控制
