
import { Layout, Typography, Menu, Avatar, Dropdown, Spin, message } from 'antd';
import { BrowserRouter, Routes, Route, Navigate, Link } from 'react-router-dom';
import { UserOutlined, SettingOutlined, LogoutOutlined, ApartmentOutlined, AlertOutlined, DatabaseOutlined } from '@ant-design/icons';
import FlowList from './pages/FlowList';
import FlowEditor from './pages/FlowEditor';
import ExecutionList from './pages/ExecutionList';
import ExecutionDetail from './pages/ExecutionDetail';
import UserManagement from './pages/UserManagement';
import RoleManagement from './pages/RoleManagement';
import PermissionManagement from './pages/PermissionManagement';
import Profile from './pages/Profile';
import RBACExample from './pages/RBACExample';
import Login from './pages/Login';
import EventCategoryManagement from './pages/EventCategoryManagement';
import EventOperationManagement from './pages/EventOperationManagement';
import DataWarehouseTask from './pages/DataWarehouseTask';
import { UserProvider, useUser } from './contexts/UserContext';
import { userAuthApi } from './services/authApi';

const { Header, Content, Sider } = Layout;
const { Title } = Typography;

// 主布局组件（需要用户上下文）
const AppLayout: React.FC = () => {
  const { user, loading, hasPermission } = useUser();

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" tip="加载用户信息..." />
      </div>
    );
  }

  // 如果用户未登录，跳转到登录页
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // 处理登出
  const handleLogout = async () => {
    try {
      await userAuthApi.logout();
      message.success('登出成功');
      window.location.href = '/sec-flow/login'; // 强制刷新并跳转
    } catch (error) {
      message.error('登出失败');
    }
  };

  // 生成菜单项
  const menuItems = [
    {
      key: 'flows',
      label: <Link to="/flows">流程管理</Link>,
      icon: <ApartmentOutlined />,
    },
    {
      key: 'datawarehouse',
      label: <Link to="/datawarehouse">数仓任务</Link>,
      icon: <DatabaseOutlined />,
    },
    {
      key: 'events',
      label: '事件管理',
      icon: <AlertOutlined />,
      children: [
        hasPermission('event_categories:read') && {
          key: 'event-categories',
          label: <Link to="/event-categories">事件分类</Link>,
        },
        hasPermission('event_operations:read') && {
          key: 'event-operations',
          label: <Link to="/event-operations">事件运营</Link>,
        },
      ].filter(Boolean),
    },
    {
      key: 'system',
      label: '系统管理',
      icon: <SettingOutlined />,
      children: [
        hasPermission('users:read') && {
          key: 'users',
          label: <Link to="/users">用户管理</Link>,
        },
        hasPermission('roles:read') && {
          key: 'roles',
          label: <Link to="/roles">角色管理</Link>,
        },
        hasPermission('permissions:read') && {
          key: 'permissions',
          label: <Link to="/permissions">权限管理</Link>,
        },
        (hasPermission('users:read') || hasPermission('roles:read') || hasPermission('permissions:read')) && {
          key: 'rbac-example',
          label: <Link to="/rbac-example">权限示例</Link>,
        },
      ].filter(Boolean),
    },
  ].filter(item => !item.children || item.children.length > 0);

  // 用户下拉菜单
  const userMenu = (
    <Menu>
      <Menu.Item key="profile" icon={<UserOutlined />}>
        <Link to="/profile">个人资料</Link>
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="logout" icon={<LogoutOutlined />} onClick={handleLogout}>
        退出登录
      </Menu.Item>
    </Menu>
  );

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ 
        background: '#fff', 
        padding: '0 24px', 
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div style={{ display: 'flex', alignItems: 'baseline', gap: '12px' }}>
          <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
            Sec Flow - 安全策略流可视化
          </Title>
          <span style={{
            fontSize: '10px',
            color: '#ccc',
            fontWeight: '300',
            fontStyle: 'italic',
            letterSpacing: '0.8px',
            opacity: 0.8
          }}>
            powered by AI
          </span>
        </div>
        
        {user && (
          <Dropdown overlay={userMenu} placement="bottomRight">
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer' }}>
              <Avatar src={user.avatar} icon={<UserOutlined />} />
              <span>{user.username}</span>
            </div>
          </Dropdown>
        )}
      </Header>
      
      <Layout>
        <Sider 
          width={200} 
          style={{ background: '#fff' }}
          breakpoint="lg"
          collapsedWidth="0"
        >
          <Menu
            mode="inline"
            style={{ height: '100%', borderRight: 0 }}
            items={menuItems}
          />
        </Sider>
        
        <Layout style={{ padding: '0 24px 24px' }}>
          <Content
            style={{
              background: '#fff',
              padding: 24,
              margin: '16px 0',
              minHeight: 280,
            }}
          >
            <Routes>
              <Route path="/flows" element={<FlowList />} />
              <Route path="/flow/:id" element={<FlowEditor />} />
              <Route path="/flow/:flowId/executions" element={<ExecutionList />} />
              <Route path="/execution/:executionId" element={<ExecutionDetail />} />

              {/* 数仓任务页面 */}
              <Route path="/datawarehouse" element={<DataWarehouseTask />} />

              {/* 系统管理页面 */}
              <Route path="/users" element={<UserManagement />} />
              <Route path="/roles" element={<RoleManagement />} />
              <Route path="/permissions" element={<PermissionManagement />} />

              {/* 事件管理页面 */}
              <Route path="/event-categories" element={<EventCategoryManagement />} />
              <Route path="/event-operations" element={<EventOperationManagement />} />

              {/* 个人资料页面 */}
              <Route path="/profile" element={<Profile />} />

              {/* RBAC示例页面 */}
              <Route path="/rbac-example" element={<RBACExample />} />

              <Route path="/" element={<Navigate to="/flows" replace />} />
            </Routes>
          </Content>
        </Layout>
      </Layout>
    </Layout>
  );
};

function App() {
  return (
    <UserProvider>
      <BrowserRouter basename="/sec-flow">
        <Routes>
          {/* 公开访问的路由（无需登录） */}
          <Route path="/login" element={<Login />} />

          {/* 需要登录的路由 */}
          <Route path="*" element={<AppLayout />} />
        </Routes>
      </BrowserRouter>
    </UserProvider>
  );
}

export default App;
