import React, { useEffect, useState, useRef } from 'react';
import { Card, Button, Space, message, Modal, Form, Input } from 'antd';
import { SaveOutlined, PlusOutlined, ClearOutlined } from '@ant-design/icons';
import LogicFlow from '@logicflow/core';
import '@logicflow/core/dist/style/index.css';
import { CustomDataWarehouseNodeDefinition, dataWarehouseNodeStyles } from '../components/CustomDataWarehouseNode';
import DataWarehouseNodeConfigForm from '../components/DataWarehouseNodeConfigForm';

interface DataWarehouseTaskData {
  nodes: any[];
  edges: any[];
}

interface NodeConfig {
  taskName: string;
  executionTime: string;
  logicDescription: string;
}

const DataWarehouseTask: React.FC = () => {
  const [taskData, setTaskData] = useState<DataWarehouseTaskData>({ nodes: [], edges: [] });
  const [loading, setLoading] = useState(false);
  const [nodeConfigVisible, setNodeConfigVisible] = useState(false);
  const [editingNodeId, setEditingNodeId] = useState<string | null>(null);
  const [editingNodeConfig, setEditingNodeConfig] = useState<NodeConfig | null>(null);
  const [pendingNodeColor, setPendingNodeColor] = useState<'red' | 'green'>('red');

  const lfRef = useRef<LogicFlow | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // 页面加载时从本地文件加载数据
    loadDataFromFile();
  }, []);

  useEffect(() => {
    if (containerRef.current) {
      setTimeout(() => {
        initLogicFlow();
      }, 100);
    }
  }, [containerRef.current]);

  // 监听自定义编辑事件
  useEffect(() => {
    const handleNodeEdit = (event: any) => {
      const { nodeId, nodeData } = event.detail;
      console.log('Edit button clicked for node:', nodeId, nodeData);
      handleNodeEditInternal(nodeData);
    };

    window.addEventListener('node-edit', handleNodeEdit);
    return () => {
      window.removeEventListener('node-edit', handleNodeEdit);
    };
  }, []);

  // 监听 taskData 变化，重新渲染流程图
  useEffect(() => {
    if (lfRef.current && taskData && (taskData.nodes?.length > 0 || taskData.edges?.length > 0)) {
      console.log('TaskData changed, re-rendering:', taskData);
      lfRef.current.render(taskData);
    }
  }, [taskData]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (lfRef.current && typeof lfRef.current.destroy === 'function') {
        try {
          lfRef.current.destroy();
        } catch (error) {
          console.warn('Failed to destroy LogicFlow instance on unmount:', error);
        }
      }
    };
  }, []);

  const initLogicFlow = () => {
    if (!containerRef.current) return;

    console.log('Initializing LogicFlow for DataWarehouse...');

    // 清除之前的实例
    if (lfRef.current && typeof lfRef.current.destroy === 'function') {
      try {
        lfRef.current.destroy();
      } catch (error) {
        console.warn('Failed to destroy LogicFlow instance:', error);
      }
    }

    // 创建新实例
    const lf = new LogicFlow({
      container: containerRef.current,
      width: containerRef.current.offsetWidth || 800,
      height: containerRef.current.offsetHeight || 600,
      grid: {
        size: 20,
        visible: true,
      },
      keyboard: {
        enabled: true,
      },
      edgeType: 'polyline',
      allowResize: true,
      allowRotate: true,
      multipleSelectKey: 'meta',
      style: {
        rect: {
          rx: 5,
          ry: 5,
          strokeWidth: 2,
        },
        circle: {
          fill: '#f5f5f5',
          stroke: '#666',
        },
        diamond: {
          fill: '#ffe6cc',
          stroke: '#d79b00',
        },
        text: {
          color: '#333',
          fontSize: 12,
        }
      }
    });

    console.log('LogicFlow instance created:', lf);

    // 注册自定义数仓任务节点
    lf.register(CustomDataWarehouseNodeDefinition);

    // 添加自定义样式
    const styleElement = document.createElement('style');
    styleElement.textContent = dataWarehouseNodeStyles;
    document.head.appendChild(styleElement);

    // 添加节点点击事件
    lf.on('node:click', ({ data }) => {
      console.log('Node clicked:', data);
    });

    // 禁用节点的文本编辑
    lf.on('node:text-edit', ({ data }) => {
      if (data.properties?.isDataWarehouseNode) {
        return false; // 阻止文本编辑
      }
    });

    // 监听自定义编辑事件
    lf.on('custom:edit-datawarehouse-node', ({ nodeId }: { nodeId: string }) => {
      console.log('Custom edit event triggered for node:', nodeId);
      const nodeData = lf.getNodeModelById(nodeId);
      if (nodeData && nodeData.properties?.isDataWarehouseNode) {
        handleNodeEditInternal(nodeData);
      }
    });

    // 如果有数据则渲染
    if (taskData && (taskData.nodes?.length > 0 || taskData.edges?.length > 0)) {
      console.log('Rendering task data:', taskData);
      lf.render(taskData);
    } else {
      // 渲染空画布
      lf.render({
        nodes: [],
        edges: []
      });
    }

    lfRef.current = lf;
    console.log('LogicFlow initialization complete for DataWarehouse');
  };

  // 从本地文件加载数据
  const loadDataFromFile = () => {
    try {
      const savedData = localStorage.getItem('dataWarehouseTaskData');
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        setTaskData(parsedData);
        message.success('已加载本地保存的数据');
      } else {
        // 如果没有保存的数据，创建一些示例节点
        createExampleNodes();
      }
    } catch (error) {
      console.error('Failed to load data from file:', error);
      message.warning('加载本地数据失败，将使用空白画布');
    }
  };

  // 创建示例节点
  const createExampleNodes = () => {
    const exampleData = {
      nodes: [
        {
          id: 'example_red_1',
          type: 'CustomDataWarehouseNode',
          x: 200,
          y: 150,
          properties: {
            config: {
              taskName: '用户数据清洗',
              executionTime: '2024-01-15 09:00:00',
              logicDescription: '从用户行为表中提取活跃用户数据，进行数据清洗和去重处理，确保数据质量'
            },
            isDataWarehouseNode: true,
            nodeColor: 'red',
          },
        },
        {
          id: 'example_green_1',
          type: 'CustomDataWarehouseNode',
          x: 500,
          y: 150,
          properties: {
            config: {
              taskName: '销售报表生成',
              executionTime: '2024-01-15 10:30:00',
              logicDescription: '汇总销售数据，生成日报、周报和月报，包含销售额、订单量等关键指标'
            },
            isDataWarehouseNode: true,
            nodeColor: 'green',
          },
        }
      ],
      edges: [
        {
          id: 'example_edge_1',
          type: 'polyline',
          sourceNodeId: 'example_red_1',
          targetNodeId: 'example_green_1',
        }
      ]
    };
    setTaskData(exampleData);
  };

  // 保存数据到本地文件
  const saveDataToFile = () => {
    if (!lfRef.current) return;

    try {
      const graphData = lfRef.current.getGraphData();
      const dataToSave = {
        nodes: graphData.nodes,
        edges: graphData.edges,
        timestamp: new Date().toISOString()
      };

      localStorage.setItem('dataWarehouseTaskData', JSON.stringify(dataToSave));
      message.success('数据已保存到本地');
    } catch (error) {
      console.error('Failed to save data to file:', error);
      message.error('保存数据失败');
    }
  };

  // 清空画布
  const clearCanvas = () => {
    if (!lfRef.current) return;

    lfRef.current.clearData();
    setTaskData({ nodes: [], edges: [] });
    localStorage.removeItem('dataWarehouseTaskData');
    message.success('画布已清空');
  };

  // 处理节点编辑
  const handleNodeEditInternal = (nodeData: any) => {
    console.log('Editing node:', nodeData);

    if (nodeData.properties?.isDataWarehouseNode) {
      const currentConfig = nodeData.properties.config || {};
      setEditingNodeId(nodeData.id);
      setEditingNodeConfig(currentConfig);
      setNodeConfigVisible(true);
    } else {
      message.info('该节点不支持编辑');
    }
  };

  // 添加红色节点
  const addRedNode = () => {
    setPendingNodeColor('red');
    setEditingNodeId(null);
    setEditingNodeConfig(null);
    setNodeConfigVisible(true);
  };

  // 添加绿色节点
  const addGreenNode = () => {
    setPendingNodeColor('green');
    setEditingNodeId(null);
    setEditingNodeConfig(null);
    setNodeConfigVisible(true);
  };

  // 处理节点配置提交
  const handleNodeConfigSubmit = (config: NodeConfig) => {
    if (!lfRef.current) return;

    if (editingNodeId) {
      // 编辑现有节点
      const nodeData = lfRef.current.getNodeModelById(editingNodeId);
      if (nodeData) {
        lfRef.current.setProperties(editingNodeId, {
          config: config,
          isDataWarehouseNode: true,
          nodeColor: nodeData.properties.nodeColor,
        });
        message.success(`已更新节点配置: ${config.taskName}`);
      }
    } else {
      // 添加新节点
      const nodeId = `datawarehouse_${Date.now()}`;
      const centerX = (lfRef.current.graphModel.width || 800) / 2;
      const centerY = (lfRef.current.graphModel.height || 600) / 2;

      const nodeConfig: any = {
        id: nodeId,
        type: 'CustomDataWarehouseNode',
        x: centerX + Math.random() * 100 - 50,
        y: centerY + Math.random() * 100 - 50,
        properties: {
          config: config,
          isDataWarehouseNode: true,
          nodeColor: pendingNodeColor,
        },
      };

      lfRef.current.addNode(nodeConfig);
      message.success(`已添加${pendingNodeColor === 'red' ? '红色' : '绿色'}节点: ${config.taskName}`);
    }

    // 关闭弹窗并重置状态
    setNodeConfigVisible(false);
    setEditingNodeId(null);
    setEditingNodeConfig(null);
  };

  return (
    <Card 
      title="数仓任务"
      extra={
        <Space>
          <Button
            icon={<ClearOutlined />}
            onClick={clearCanvas}
          >
            清空
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={saveDataToFile}
            loading={loading}
          >
            保存
          </Button>
        </Space>
      }
      style={{ height: 'calc(100vh - 100px)' }}
    >
      {/* 节点工具栏 */}
      <div style={{ marginBottom: '16px', padding: '12px', background: '#fafafa', borderRadius: '6px' }}>
        <Space>
          <span style={{ fontWeight: 'bold' }}>添加节点:</span>
          <Button
            size="small"
            type="primary"
            icon={<PlusOutlined />}
            style={{
              backgroundColor: '#ff4d4f',
              borderColor: '#ff4d4f',
              color: '#fff'
            }}
            onClick={addRedNode}
          >
            添加红色节点
          </Button>
          <Button
            size="small"
            type="primary"
            icon={<PlusOutlined />}
            style={{
              backgroundColor: '#52c41a',
              borderColor: '#52c41a',
              color: '#fff'
            }}
            onClick={addGreenNode}
          >
            添加绿色节点
          </Button>
          <span style={{ marginLeft: '20px', color: '#666' }}>
            💡 提示: 拖拽节点的锚点可以创建连线
          </span>
        </Space>
      </div>

      <div
        ref={containerRef}
        style={{
          width: '100%',
          height: 'calc(100% - 80px)',
          minHeight: '500px',
          border: '1px solid #d9d9d9',
          borderRadius: '4px',
          position: 'relative'
        }}
      />
      
      {/* 节点配置表单 */}
      <Modal
        title={`${editingNodeId ? '编辑' : '配置'}${pendingNodeColor === 'red' ? '红色' : '绿色'}节点`}
        open={nodeConfigVisible}
        onCancel={() => {
          setNodeConfigVisible(false);
          setEditingNodeId(null);
          setEditingNodeConfig(null);
        }}
        footer={null}
        width={600}
        zIndex={1100}
      >
        <DataWarehouseNodeConfigForm
          onSubmit={handleNodeConfigSubmit}
          onCancel={() => {
            setNodeConfigVisible(false);
            setEditingNodeId(null);
            setEditingNodeConfig(null);
          }}
          initialValues={editingNodeConfig || undefined}
        />
      </Modal>
    </Card>
  );
};

export default DataWarehouseTask;
