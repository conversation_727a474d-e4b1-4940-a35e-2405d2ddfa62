import { HtmlNode, HtmlNodeModel } from '@logicflow/core';

// 自定义数仓任务节点类
class CustomDataWarehouseNode extends HtmlNode {
  setHtml(rootEl: HTMLElement) {
    const { properties } = this.props.model;
    const { config = {}, nodeColor = 'red' } = properties;
    const { taskName = '未命名任务', executionTime = '未设置', logicDescription = '暂无描述' } = config;

    const el = document.createElement('div');
    el.className = 'datawarehouse-node-wrapper';

    // 根据节点颜色设置容器样式类
    let containerClass = 'datawarehouse-node-container';
    if (nodeColor === 'red') {
      containerClass += ' red-node';
    } else if (nodeColor === 'green') {
      containerClass += ' green-node';
    }

    // 根据节点颜色选择图标
    const nodeIcon = nodeColor === 'red' ? '🔴' : '🟢';

    // 截断长文本
    const truncateText = (text: string, maxLength: number) => {
      if (!text) return '未设置';
      return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    };

    el.innerHTML = `
      <div class="${containerClass}">
        <div class="datawarehouse-node-header">
          <div class="node-icon">${nodeIcon}</div>
          <div class="node-title">${truncateText(taskName, 12)}</div>
          <button class="edit-btn" onclick="editDataWarehouseNode('${this.props.model.id}')">
            <span class="edit-icon">✏️</span>
          </button>
        </div>
        <div class="datawarehouse-node-body">
          <div class="config-item">
            <span class="config-label">任务名称:</span>
            <span class="config-value">${truncateText(taskName, 15)}</span>
          </div>
          <div class="config-item">
            <span class="config-label">执行时间:</span>
            <span class="config-value">${truncateText(executionTime, 15)}</span>
          </div>
          <div class="config-item">
            <span class="config-label">逻辑描述:</span>
            <span class="config-value">${truncateText(logicDescription, 20)}</span>
          </div>
        </div>
      </div>
    `;
    
    rootEl.innerHTML = '';
    rootEl.appendChild(el);

    // 全局编辑函数
    (window as any).editDataWarehouseNode = (nodeId: string) => {
      const { graphModel } = this.props;
      graphModel.eventCenter.emit('custom:edit-datawarehouse-node', { nodeId });
    };
  }
}

class CustomDataWarehouseNodeModel extends HtmlNodeModel {
  setAttributes() {
    this.width = 260;
    this.height = 180;
    this.text.editable = false;
    // 隐藏默认文本显示
    this.text.value = '';
  }

  getDefaultAnchor() {
    const { width, height, x, y, id } = this;

    // 标准的四个锚点
    return [
      {
        x: x - width / 2,
        y,
        name: 'left',
        id: `${id}_left`,
      },
      {
        x: x + width / 2,
        y,
        name: 'right',
        id: `${id}_right`,
      },
      {
        x,
        y: y - height / 2,
        name: 'top',
        id: `${id}_top`,
      },
      {
        x,
        y: y + height / 2,
        name: 'bottom',
        id: `${id}_bottom`,
      },
    ];
  }
}

export const CustomDataWarehouseNodeDefinition = {
  type: 'CustomDataWarehouseNode',
  view: CustomDataWarehouseNode,
  model: CustomDataWarehouseNodeModel,
};

// CSS 样式
export const dataWarehouseNodeStyles = `
/* 隐藏 HTML 节点的默认文本层 */
.lf-node[data-node-type="CustomDataWarehouseNode"] .lf-node-text {
  display: none !important;
}

.lf-node[data-node-type="CustomDataWarehouseNode"] .lf-node-text-auto-wrap {
  display: none !important;
}

.datawarehouse-node-wrapper {
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.datawarehouse-node-container {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  border: 2px solid;
  box-sizing: border-box;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 红色节点样式 */
.datawarehouse-node-container.red-node {
  background: linear-gradient(135deg, #fff2f0 0%, #ffebe6 100%);
  border-color: #ff4d4f;
  box-shadow: 0 2px 8px rgba(255, 77, 79, 0.15);
}

.datawarehouse-node-container.red-node:hover {
  box-shadow: 0 4px 16px rgba(255, 77, 79, 0.25);
  transform: translateY(-1px);
}

.datawarehouse-node-container.red-node .datawarehouse-node-header {
  background: linear-gradient(90deg, #ff4d4f, #ff7875);
}

.datawarehouse-node-container.red-node .config-value {
  background: rgba(255, 77, 79, 0.1);
  color: #d4380d;
}

/* 绿色节点样式 */
.datawarehouse-node-container.green-node {
  background: linear-gradient(135deg, #f6ffed 0%, #e6f7ff 100%);
  border-color: #52c41a;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.15);
}

.datawarehouse-node-container.green-node:hover {
  box-shadow: 0 4px 16px rgba(82, 196, 26, 0.25);
  transform: translateY(-1px);
}

.datawarehouse-node-container.green-node .datawarehouse-node-header {
  background: linear-gradient(90deg, #52c41a, #73d13d);
}

.datawarehouse-node-container.green-node .config-value {
  background: rgba(82, 196, 26, 0.1);
  color: #389e0d;
}

.datawarehouse-node-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  color: white;
  font-weight: 600;
  font-size: 13px;
  position: relative;
}

.node-icon {
  font-size: 16px;
  margin-right: 6px;
}

.node-title {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 700;
}

.edit-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  color: white;
  cursor: pointer;
  padding: 2px 6px;
  font-size: 12px;
  transition: all 0.2s ease;
}

.edit-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.edit-icon {
  font-size: 12px;
}

.datawarehouse-node-body {
  padding: 12px;
  font-size: 12px;
  line-height: 1.4;
  height: calc(100% - 40px);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.config-label {
  color: #666;
  font-weight: 600;
  font-size: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.config-value {
  font-weight: 600;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 11px;
  word-break: break-all;
  line-height: 1.4;
  min-height: 24px;
  display: flex;
  align-items: center;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* 滚动条样式 */
.datawarehouse-node-body::-webkit-scrollbar {
  width: 3px;
}

.datawarehouse-node-body::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 2px;
}

.datawarehouse-node-body::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 2px;
}

.datawarehouse-node-body::-webkit-scrollbar-thumb:hover {
  background: #999;
}
`;
