import React from 'react';
import { Form, Input, Button, Space } from 'antd';

interface NodeConfig {
  taskName: string;
  executionTime: string;
  logicDescription: string;
}

interface DataWarehouseNodeConfigFormProps {
  onSubmit: (config: NodeConfig) => void;
  onCancel: () => void;
  initialValues?: NodeConfig;
}

const DataWarehouseNodeConfigForm: React.FC<DataWarehouseNodeConfigFormProps> = ({
  onSubmit,
  onCancel,
  initialValues
}) => {
  const [form] = Form.useForm();

  const handleSubmit = (values: any) => {
    const config: NodeConfig = {
      taskName: values.taskName,
      executionTime: values.executionTime || '',
      logicDescription: values.logicDescription,
    };
    onSubmit(config);
  };

  // 处理初始值
  const getInitialValues = () => {
    if (!initialValues) return {};

    return {
      taskName: initialValues.taskName,
      executionTime: initialValues.executionTime,
      logicDescription: initialValues.logicDescription,
    };
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      initialValues={getInitialValues()}
    >
      <Form.Item
        name="taskName"
        label="任务名称"
        rules={[
          { required: true, message: '请输入任务名称' },
          { max: 50, message: '任务名称不能超过50个字符' }
        ]}
      >
        <Input 
          placeholder="请输入任务名称，如：用户数据清洗" 
          maxLength={50}
        />
      </Form.Item>

      <Form.Item
        name="executionTime"
        label="执行时间"
        rules={[
          { required: true, message: '请输入执行时间' },
          { max: 50, message: '执行时间不能超过50个字符' }
        ]}
      >
        <Input
          placeholder="请输入执行时间，如：每天09:00、2024-01-15 10:30:00"
          maxLength={50}
        />
      </Form.Item>

      <Form.Item
        name="logicDescription"
        label="逻辑描述"
        rules={[
          { required: true, message: '请输入逻辑描述' },
          { max: 200, message: '逻辑描述不能超过200个字符' }
        ]}
      >
        <Input.TextArea
          placeholder="请详细描述任务的业务逻辑，如：从用户行为表中提取活跃用户数据，进行数据清洗和去重处理"
          rows={4}
          maxLength={200}
          showCount
        />
      </Form.Item>

      <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
        <Space>
          <Button onClick={onCancel}>
            取消
          </Button>
          <Button type="primary" htmlType="submit">
            确定
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );
};

export default DataWarehouseNodeConfigForm;
