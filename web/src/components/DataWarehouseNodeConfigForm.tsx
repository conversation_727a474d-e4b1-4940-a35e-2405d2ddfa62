import React from 'react';
import { Form, Input, Button, Space, DatePicker } from 'antd';
import dayjs from 'dayjs';

interface NodeConfig {
  taskName: string;
  executionTime: string;
  logicDescription: string;
}

interface DataWarehouseNodeConfigFormProps {
  onSubmit: (config: NodeConfig) => void;
  onCancel: () => void;
  initialValues?: NodeConfig;
}

const DataWarehouseNodeConfigForm: React.FC<DataWarehouseNodeConfigFormProps> = ({
  onSubmit,
  onCancel,
  initialValues
}) => {
  const [form] = Form.useForm();

  const handleSubmit = (values: any) => {
    const config: NodeConfig = {
      taskName: values.taskName,
      executionTime: values.executionTime ? values.executionTime.format('YYYY-MM-DD HH:mm:ss') : (values.executionTimeText || ''),
      logicDescription: values.logicDescription,
    };
    onSubmit(config);
  };

  // 处理初始值，如果executionTime是日期字符串，转换为dayjs对象
  const getInitialValues = () => {
    if (!initialValues) return {};
    
    const values: any = {
      taskName: initialValues.taskName,
      logicDescription: initialValues.logicDescription,
    };

    // 尝试解析executionTime为日期
    if (initialValues.executionTime) {
      try {
        const parsedDate = dayjs(initialValues.executionTime);
        if (parsedDate.isValid()) {
          values.executionTime = parsedDate;
        } else {
          values.executionTimeText = initialValues.executionTime;
        }
      } catch {
        values.executionTimeText = initialValues.executionTime;
      }
    }

    return values;
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      initialValues={getInitialValues()}
    >
      <Form.Item
        name="taskName"
        label="任务名称"
        rules={[
          { required: true, message: '请输入任务名称' },
          { max: 50, message: '任务名称不能超过50个字符' }
        ]}
      >
        <Input 
          placeholder="请输入任务名称，如：用户数据清洗" 
          maxLength={50}
        />
      </Form.Item>

      <Form.Item
        name="executionTime"
        label="执行时间"
        rules={[
          { required: true, message: '请选择执行时间' }
        ]}
      >
        <DatePicker
          showTime
          format="YYYY-MM-DD HH:mm:ss"
          placeholder="选择任务执行时间"
          style={{ width: '100%' }}
        />
      </Form.Item>

      {/* 备用文本输入框，如果日期解析失败时显示 */}
      <Form.Item
        name="executionTimeText"
        label="执行时间（文本）"
        style={{ display: 'none' }}
      >
        <Input placeholder="请输入执行时间描述" />
      </Form.Item>

      <Form.Item
        name="logicDescription"
        label="逻辑描述"
        rules={[
          { required: true, message: '请输入逻辑描述' },
          { max: 200, message: '逻辑描述不能超过200个字符' }
        ]}
      >
        <Input.TextArea
          placeholder="请详细描述任务的业务逻辑，如：从用户行为表中提取活跃用户数据，进行数据清洗和去重处理"
          rows={4}
          maxLength={200}
          showCount
        />
      </Form.Item>

      <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
        <Space>
          <Button onClick={onCancel}>
            取消
          </Button>
          <Button type="primary" htmlType="submit">
            确定
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );
};

export default DataWarehouseNodeConfigForm;
